Config = {}

-- General Settings
Config.Debug = true -- Enable for troubleshooting
Config.UseTarget = GetConvar('UseTarget', 'false') == 'true'

-- Hunting License Settings
Config.RequireLicense = true
Config.LicenseItem = 'hunting_license'

-- Tent Settings
Config.TentModel = 'prop_skid_tent_01'
Config.TentPlacementCooldown = 300000 -- 5 minutes in milliseconds
Config.TentRadius = 50.0 -- Radius around tent where bait can be placed
Config.MaxTentsPerPlayer = 1

-- Bait Settings
Config.BaitItem = 'hunting_bait'
Config.BaitDuration = 600000 -- 10 minutes in milliseconds
Config.BaitSpawnDelay = 15000 -- Reduced to 15 seconds for testing
Config.BaitRadius = 80.0 -- Reduced radius for better spawn control

-- Animal Settings
Config.Animals = {
    {
        model = 'a_c_deer',
        spawnChance = 80, -- Increased spawn chance for testing
        maxSpawns = 2, -- Reduced max spawns to prevent overcrowding
        rewards = {
            {item = 'raw_meat', min = 2, max = 4},
            {item = 'animal_pelt', min = 1, max = 2},
            {item = 'deer_antlers', min = 0, max = 1, chance = 15} -- 15% chance for rare drop
        }
    },
    {
        model = 'a_c_boar',
        spawnChance = 60, -- Increased spawn chance
        maxSpawns = 2,
        rewards = {
            {item = 'raw_meat', min = 3, max = 5},
            {item = 'animal_pelt', min = 1, max = 1},
            {item = 'boar_tusk', min = 0, max = 1, chance = 20} -- 20% chance for rare drop
        }
    },
    {
        model = 'a_c_mtlion',
        spawnChance = 40, -- Increased spawn chance for testing
        maxSpawns = 1,
        rewards = {
            {item = 'raw_meat', min = 4, max = 6},
            {item = 'animal_pelt', min = 2, max = 3},
            {item = 'mountain_lion_tooth', min = 0, max = 1, chance = 25} -- 25% chance for rare drop
        }
    }
}

-- Hunting Settings
Config.HuntingKnife = 'weapon_knife' -- Use GTA 5 default knife weapon
Config.SkinningTime = 8000 -- 8 seconds
Config.CorpseCleanupTime = 300000 -- 5 minutes
Config.MaxAnimalsPerZone = 10

-- Weapon Settings
Config.HuntingWeapons = {
    'weapon_sniperrifle',
    'weapon_musket',
    'weapon_marksmanrifle',
    'weapon_heavysniper',
    'weapon_huntingrifle' -- If you have custom hunting rifle
}

Config.RecommendedWeapon = 'weapon_sniperrifle' -- Default hunting rifle

-- Animation Settings
Config.SkinningAnim = {
    dict = 'amb@medic@standing@kneel@base',
    anim = 'base',
    flag = 1
}

Config.KneelingAnim = {
    dict = 'amb@world_human_bum_wash@male@low@base',
    anim = 'base',
    flag = 1
}

Config.TentPlaceAnim = {
    dict = 'weapons@first_person@aim_rng@generic@projectile@sticky_bomb@',
    anim = 'plant_floor',
    flag = 16
}

-- Notification Settings
Config.Notifications = {
    tentPlaced = 'Hunting tent has been set up successfully!',
    tentRemoved = 'Hunting tent has been packed up.',
    baitPlaced = 'Hunting bait has been placed. Animals will arrive soon...',
    baitConsumed = 'The bait has been consumed by wildlife.',
    animalSkinned = 'You have successfully skinned the animal.',
    noLicense = 'You need a hunting license to hunt!',
    noKnife = 'You need a hunting knife to skin animals!',
    tentCooldown = 'You must wait before placing another tent.',
    tooFarFromTent = 'You are too far from your tent to place bait here.',
    alreadyHasTent = 'You already have a tent placed!',
    alreadySkinned = 'This animal has already been skinned.',
    animalSpawned = 'Wildlife has been attracted to your bait!'
}

-- Restricted Areas (where hunting is not allowed)
Config.RestrictedAreas = {
    {coords = vector3(-1037.0, -2737.0, 20.0), radius = 500.0}, -- Los Santos Airport
    {coords = vector3(240.0, -1379.0, 33.0), radius = 300.0}, -- Central Los Santos
}
