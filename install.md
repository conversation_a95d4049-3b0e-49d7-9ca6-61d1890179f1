# QB-Hunting Installation Guide

## Quick Setup Checklist

### 1. Prerequisites Check
- [ ] Qbox Framework installed and running
- [ ] ox_inventory installed and configured
- [ ] ox_lib installed

### 2. File Installation
- [ ] Copy `qb-hunting` folder to `/resources/[qb]/`
- [ ] Add `ensure qb-hunting` to server.cfg
- [ ] Restart server or `refresh` and `start qb-hunting`

### 3. Item Configuration

#### Option A: Manual Item Addition
Copy the items from `items.lua` to your `ox_inventory/data/items.lua` file.

#### Option B: Automatic Item Registration (Recommended)
If your ox_inventory supports external item files, the items will be automatically loaded.

### 4. Verification Steps

1. **Check Resource Status**
   ```
   In server console: ensure qb-hunting
   Should show: Started qb-hunting
   ```

2. **Test Item Spawning**
   ```
   In-game: /give [playerid] hunting_license 1
   In-game: /give [playerid] hunting_bag 1
   In-game: /give [playerid] hunting_bait 5
   In-game: /give [playerid] hunting_knife 1
   ```

3. **Test Basic Functionality**
   - Use hunting bag to place tent
   - Use bait near tent
   - Wait for animals to spawn
   - Test skinning with knife

### 5. Configuration (Optional)

Edit `shared/config.lua` to customize:

```lua
-- Reduce cooldowns for testing
Config.TentPlacementCooldown = 60000 -- 1 minute instead of 5

-- Increase spawn chances for testing
Config.Animals[1].spawnChance = 100 -- Deer always spawn

-- Enable debug mode
Config.Debug = true
```

### 6. Troubleshooting

#### Common Issues:

**"You need a hunting license to hunt!"**
- Solution: Give player hunting_license item
- Command: `/give [playerid] hunting_license 1`

**Items not working when used**
- Check ox_inventory item exports are properly configured
- Verify items.lua is properly integrated
- Check server console for errors

**Animals not spawning**
- Enable debug mode in config
- Check server console for spawn messages
- Verify bait is placed within tent radius

**Tent not placing**
- Check for restricted areas in config
- Verify player has hunting license
- Check placement cooldown hasn't expired

#### Debug Commands:

```lua
-- Enable debug in config.lua
Config.Debug = true

-- Check hunting stats (admin only)
/huntingstats [playerid]

-- Remove tent manually
/removetent
```

### 7. Performance Optimization

For high-population servers:

```lua
-- Reduce max animals per zone
Config.MaxAnimalsPerZone = 5

-- Reduce corpse cleanup time
Config.CorpseCleanupTime = 180000 -- 3 minutes

-- Increase placement cooldown
Config.TentPlacementCooldown = 600000 -- 10 minutes
```

### 8. Integration with Other Scripts

#### Shops Integration
Add hunting items to your shop script:

```lua
-- Example for qb-shops
['hunting_shop'] = {
    ['hunting_license'] = 500,
    ['hunting_bag'] = 150,
    ['hunting_bait'] = 25,
    ['hunting_knife'] = 75,
}
```

#### Job Integration
Restrict hunting license sales to specific jobs:

```lua
-- In your shop/vendor script
if PlayerData.job.name == 'police' or PlayerData.job.name == 'ranger' then
    -- Allow license sales
end
```

### 9. Support

If you encounter issues:

1. Check server console for errors
2. Enable debug mode in config
3. Verify all dependencies are installed
4. Check item configuration in ox_inventory
5. Test with minimal configuration first

### 10. Post-Installation

After successful installation:

- [ ] Test all hunting mechanics
- [ ] Configure restricted areas for your map
- [ ] Set up hunting shops/vendors
- [ ] Train staff on hunting system
- [ ] Create server rules for hunting areas

## Success Indicators

✅ Resource starts without errors
✅ Items can be spawned and used
✅ Tent placement works
✅ Animals spawn after bait placement
✅ Skinning provides rewards
✅ No console errors during gameplay

Your QB-Hunting script is now ready for production use!
