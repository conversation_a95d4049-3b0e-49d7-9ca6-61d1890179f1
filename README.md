# QB-Hunting - Qbox Framework

A comprehensive hunting script for FiveM servers using the Qbox framework. This script provides an immersive hunting experience with tent placement, bait systems, and animal harvesting mechanics.

## Features

### 🏕️ **Tent Placement System**
- Deploy hunting tents using `hunting_bag` item
- Persistent tent placement until removed or player disconnects
- Configurable placement cooldowns and restrictions
- Visual tent props with proper physics

### 🎣 **Bait and Animal Spawning**
- Place `hunting_bait` within tent radius to attract animals
- Realistic spawn delays and animal behavior
- Multiple animal types: Deer, Boar, Mountain Lion
- Animals pathfind toward bait and remain in hunting zone

### 🔪 **Hunting and Harvesting**
- Shoot animals to create lootable corpses
- Use `hunting_knife` to skin animals with animations
- Prevent multiple skinning attempts
- Reward system with rare drops

### 🎮 **Gameplay Mechanics**
- Hunting license requirement system
- Anti-exploit measures with cooldowns
- Automatic cleanup of old corpses
- Performance-optimized animal management

## Installation

### Prerequisites
- Qbox Framework
- ox_inventory
- ox_lib

### Setup Instructions

1. **Download and Extract**
   ```bash
   cd /path/to/your/server/resources/[qb]/
   git clone <repository-url> qb-hunting
   ```

2. **Add to server.cfg**
   ```cfg
   ensure qb-hunting
   ```

3. **Add Items to ox_inventory**
   
   Copy the contents of `items.lua` to your `ox_inventory/data/items.lua` file, or if using a separate items file, ensure the items are properly registered.

4. **Configure the Script**
   
   Edit `shared/config.lua` to customize:
   - Animal spawn rates and rewards
   - Tent placement restrictions
   - Bait duration and effectiveness
   - Required items and permissions

5. **Add Item Images (Optional)**
   
   Add the following images to your `ox_inventory/web/images/` folder:
   - `hunting_license.png`
   - `hunting_bag.png`
   - `hunting_bait.png`
   - `hunting_knife.png`
   - `raw_meat.png`
   - `animal_pelt.png`
   - `deer_antlers.png`
   - `mountain_lion_tooth.png`
   - `boar_tusk.png`

## Usage

### For Players

1. **Get Started**
   - Obtain a `hunting_license` from appropriate vendor
   - Purchase `hunting_bag`, `hunting_bait`, and `hunting_knife`

2. **Set Up Camp**
   - Use the hunting bag to place a tent in a suitable location
   - Avoid restricted areas (airports, city centers)

3. **Attract Animals**
   - Place bait within 50 meters of your tent
   - Wait 45 seconds for animals to spawn
   - Animals will move toward the bait location

4. **Hunt and Harvest**
   - Shoot animals with any weapon
   - Use hunting knife on dead animals to skin them
   - Collect raw meat, pelts, and rare trophies

5. **Pack Up**
   - Use `/removetent` command to pack up your camp
   - Tent automatically removes on disconnect

### For Administrators

- **View Hunting Stats**: `/huntingstats [playerid]`
- **Debug Mode**: Set `Config.Debug = true` in config file
- **Permissions**: Adjust admin permissions in server script

## Configuration

### Key Settings

```lua
-- Tent Settings
Config.TentPlacementCooldown = 300000 -- 5 minutes
Config.TentRadius = 50.0 -- Bait placement radius

-- Bait Settings  
Config.BaitDuration = 600000 -- 10 minutes
Config.BaitSpawnDelay = 45000 -- 45 seconds

-- Animal Settings
Config.MaxAnimalsPerZone = 10
Config.CorpseCleanupTime = 300000 -- 5 minutes
```

### Animal Configuration

Each animal type can be customized with:
- Spawn chance percentage
- Maximum spawns per bait
- Reward items and quantities
- Rare drop chances

### Restricted Areas

Add areas where hunting is prohibited:
```lua
Config.RestrictedAreas = {
    {coords = vector3(x, y, z), radius = 500.0},
}
```

## Items

| Item | Description | Weight | Stackable |
|------|-------------|---------|-----------|
| `hunting_license` | Required for hunting activities | 50 | No |
| `hunting_bag` | Deploys hunting tent | 2000 | No |
| `hunting_bait` | Attracts animals | 500 | Yes |
| `hunting_knife` | Skins dead animals | 300 | No |
| `raw_meat` | Basic animal product | 200 | Yes |
| `animal_pelt` | Valuable crafting material | 800 | Yes |
| `deer_antlers` | Rare trophy (15% chance) | 1200 | Yes |
| `boar_tusk` | Rare trophy (20% chance) | 150 | Yes |
| `mountain_lion_tooth` | Rare trophy (25% chance) | 50 | Yes |

## Performance

- Efficient animal spawn/despawn system
- Automatic cleanup prevents entity buildup
- Optimized threading for continuous checks
- Server-side validation prevents exploits

## Support

For issues, suggestions, or contributions:
1. Check existing issues in the repository
2. Create detailed bug reports with reproduction steps
3. Include server logs and configuration details

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Credits

- Qbox Framework Team
- ox_inventory by Overextended
- Community contributors and testers
