fx_version 'cerulean'
game 'gta5'

name 'qb-hunting'
description 'QBCore Hunting Script with Tent Placement, Bait System, and Animal Harvesting'
author 'QBCore Community'
version '1.0.0'

shared_scripts {
    '@qb-core/shared/locale.lua',
    'shared/config.lua'
}

client_scripts {
    'client/main.lua'
}

server_scripts {
    'server/main.lua'
}

files {
    'items.lua'
}

dependencies {
    'qb-core',
    'qb-inventory',
    'qb-progressbar'
}

lua54 'yes'
