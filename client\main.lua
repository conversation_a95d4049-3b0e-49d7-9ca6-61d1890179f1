local PlayerData = {}
local isLoggedIn = false

-- Player state variables
local playerTent = nil
local playerBait = {}
local spawnedAnimals = {}
local lastTentPlacement = 0
local huntingZone = false

-- Initialize player data
RegisterNetEvent('qbx_core:client:playerLoaded', function()
    PlayerData = exports.qbx_core:GetPlayerData()
    isLoggedIn = true
end)

RegisterNetEvent('qbx_core:client:playerLoggedOut', function()
    PlayerData = {}
    isLoggedIn = false
    CleanupHuntingSession()
end)

RegisterNetEvent('qbx_core:client:onPlayerDataUpdate', function(val)
    PlayerData = val
end)

-- Utility Functions
local function DebugPrint(message)
    if Config.Debug then
        print('[Qbox-Hunting] ' .. message)
    end
end

local function ShowNotification(message, type)
    lib.notify({
        title = 'Hunting',
        description = message,
        type = type or 'inform'
    })
end

local function HasItem(item)
    local hasItem = exports.ox_inventory:Search('count', item) > 0
    return hasItem
end

local function IsInRestrictedArea(coords)
    for _, area in pairs(Config.RestrictedAreas) do
        if #(coords - area.coords) < area.radius then
            return true
        end
    end
    return false
end

local function LoadAnimDict(dict)
    if not HasAnimDictLoaded(dict) then
        RequestAnimDict(dict)
        while not HasAnimDictLoaded(dict) do
            Wait(1)
        end
    end
end

local function LoadModel(model)
    if not HasModelLoaded(model) then
        RequestModel(model)
        while not HasModelLoaded(model) do
            Wait(1)
        end
    end
end

-- Tent Management Functions
local function PlaceTent()
    local playerPed = PlayerPedId()
    local coords = GetEntityCoords(playerPed)
    
    -- Check if player has license
    if Config.RequireLicense and not HasItem(Config.LicenseItem) then
        ShowNotification(Config.Notifications.noLicense, 'error')
        return
    end
    
    -- Check if in restricted area
    if IsInRestrictedArea(coords) then
        ShowNotification('You cannot hunt in this area!', 'error')
        return
    end
    
    -- Check cooldown
    if GetGameTimer() - lastTentPlacement < Config.TentPlacementCooldown then
        ShowNotification(Config.Notifications.tentCooldown, 'error')
        return
    end
    
    -- Check if player already has a tent
    if playerTent then
        ShowNotification(Config.Notifications.alreadyHasTent, 'error')
        return
    end
    
    -- Play animation
    LoadAnimDict(Config.TentPlaceAnim.dict)
    TaskPlayAnim(playerPed, Config.TentPlaceAnim.dict, Config.TentPlaceAnim.anim, 8.0, -8.0, -1, Config.TentPlaceAnim.flag, 0, false, false, false)
    
    -- Progress bar
    if lib.progressBar({
        duration = 5000,
        label = 'Setting up hunting tent...',
        useWhileDead = false,
        canCancel = true,
        disable = {
            car = true,
            move = true,
            combat = true,
        },
    }) then
        ClearPedTasks(playerPed)
        
        -- Load tent model
        LoadModel(Config.TentModel)

        -- Create tent with proper ground placement
        local tentCoords = GetOffsetFromEntityInWorldCoords(playerPed, 0.0, 2.0, 0.0)

        -- Multiple attempts to get proper ground Z coordinate
        local groundZ = 0.0
        local attempts = 0

        while groundZ == 0.0 and attempts < 5 do
            groundZ = GetGroundZFor_3dCoord(tentCoords.x, tentCoords.y, tentCoords.z + 100.0, false)
            attempts = attempts + 1
            if groundZ == 0.0 then
                Wait(100) -- Wait a bit and try again
            end
        end

        -- If still no ground found, use player's Z coordinate
        if groundZ == 0.0 then
            groundZ = tentCoords.z
            DebugPrint('Ground detection failed, using player Z: ' .. groundZ)
        else
            DebugPrint('Ground detected at Z: ' .. groundZ)
        end

        -- Create tent at ground level
        local tent = CreateObject(Config.TentModel, tentCoords.x, tentCoords.y, groundZ + 0.1, true, true, true)

        -- Wait for object to be created
        local timeout = 0
        while not DoesEntityExist(tent) and timeout < 50 do
            Wait(10)
            timeout = timeout + 1
        end

        if DoesEntityExist(tent) then
            -- Ensure tent is properly placed on ground
            PlaceObjectOnGroundProperly(tent)

            -- Final position adjustment
            local finalCoords = GetEntityCoords(tent)
            DebugPrint('Tent placed at final coords: ' .. tostring(finalCoords))

            FreezeEntityPosition(tent, true)
        else
            DebugPrint('Failed to create tent object!')
            ShowNotification('Failed to place tent. Try again.', 'error')
            return
        end
        
        playerTent = {
            object = tent,
            coords = GetEntityCoords(tent)
        }
        
        lastTentPlacement = GetGameTimer()
        huntingZone = true
        
        -- Remove hunting bag from inventory
        TriggerServerEvent('qbox-hunting:server:removeItem', 'hunting_bag', 1)
        ShowNotification(Config.Notifications.tentPlaced, 'success')
        
        DebugPrint('Tent placed at: ' .. tostring(tentCoords))
    else
        ClearPedTasks(playerPed)
        ShowNotification('Tent placement cancelled', 'error')
    end
end

local function RemoveTent(returnBag)
    if not playerTent then
        return
    end

    if DoesEntityExist(playerTent.object) then
        DeleteEntity(playerTent.object)
    end

    -- Clean up any active bait and animals
    CleanupHuntingSession()

    -- Return hunting bag to player if requested
    if returnBag then
        TriggerServerEvent('qbox-hunting:server:giveItem', 'hunting_bag', 1)
        ShowNotification('Tent packed up and hunting bag returned to inventory.', 'success')
    else
        ShowNotification(Config.Notifications.tentRemoved, 'success')
    end

    playerTent = nil
    huntingZone = false

    DebugPrint('Tent removed (bag returned: ' .. tostring(returnBag) .. ')')
end

local function PickupTent()
    if not playerTent then
        ShowNotification('You don\'t have a tent placed!', 'error')
        return
    end

    local playerPed = PlayerPedId()
    local playerCoords = GetEntityCoords(playerPed)
    local tentCoords = playerTent.coords
    local distance = #(playerCoords - tentCoords)

    -- Check if player is close enough to tent
    if distance > 5.0 then
        ShowNotification('You need to be closer to your tent to pack it up!', 'error')
        return
    end

    -- Play packing animation
    LoadAnimDict(Config.TentPlaceAnim.dict)
    TaskPlayAnim(playerPed, Config.TentPlaceAnim.dict, Config.TentPlaceAnim.anim, 8.0, -8.0, -1, Config.TentPlaceAnim.flag, 0, false, false, false)

    -- Progress bar for packing
    if lib.progressBar({
        duration = 3000,
        label = 'Packing up tent...',
        useWhileDead = false,
        canCancel = true,
        disable = {
            car = true,
            move = true,
            combat = true,
        },
    }) then
        ClearPedTasks(playerPed)
        RemoveTent(true) -- Return bag to inventory
    else
        ClearPedTasks(playerPed)
        ShowNotification('Tent packing cancelled', 'error')
    end
end

-- Animal Management Functions
local function SpawnAnimalsNearBait(baitId)
    if not playerBait[baitId] then
        DebugPrint('Bait ID ' .. baitId .. ' not found, aborting spawn')
        return
    end

    local baitCoords = playerBait[baitId].coords
    local totalAnimals = 0

    DebugPrint('Starting animal spawn process at bait coords: ' .. tostring(baitCoords))
    DebugPrint('Current spawned animals count: ' .. #spawnedAnimals)

    -- Check if we already have too many animals
    local currentAnimalCount = 0
    for animal, _ in pairs(spawnedAnimals) do
        if DoesEntityExist(animal) then
            currentAnimalCount = currentAnimalCount + 1
        end
    end

    if currentAnimalCount >= Config.MaxAnimalsPerZone then
        DebugPrint('Max animals already spawned (' .. currentAnimalCount .. '), skipping spawn')
        ShowNotification('Too many animals in the area. Wait for some to leave or be hunted.', 'info')
        return
    end

    for _, animalConfig in pairs(Config.Animals) do
        local spawnRoll = math.random(100)
        DebugPrint('Spawn roll for ' .. animalConfig.model .. ': ' .. spawnRoll .. ' (need <= ' .. animalConfig.spawnChance .. ')')

        if spawnRoll <= animalConfig.spawnChance and totalAnimals < Config.MaxAnimalsPerZone then
            local spawnCount = math.random(1, animalConfig.maxSpawns)
            DebugPrint('Attempting to spawn ' .. spawnCount .. ' ' .. animalConfig.model .. '(s)')

            for i = 1, spawnCount do
                if totalAnimals >= Config.MaxAnimalsPerZone then
                    DebugPrint('Max animals reached, stopping spawn')
                    break
                end

                -- Find spawn position around bait
                local attempts = 0
                local animal = nil

                while not animal and attempts < 10 do
                    attempts = attempts + 1

                    local angle = math.random() * 2 * math.pi
                    local distance = math.random(30, 60) -- Closer spawn range
                    local spawnCoords = vector3(
                        baitCoords.x + math.cos(angle) * distance,
                        baitCoords.y + math.sin(angle) * distance,
                        baitCoords.z + 5.0
                    )

                    -- Get proper ground Z coordinate with multiple attempts
                    local groundZ = 0.0
                    local groundAttempts = 0

                    while groundZ == 0.0 and groundAttempts < 3 do
                        groundZ = GetGroundZFor_3dCoord(spawnCoords.x, spawnCoords.y, spawnCoords.z + 100.0, false)
                        groundAttempts = groundAttempts + 1
                        if groundZ == 0.0 then
                            Wait(50)
                        end
                    end

                    if groundZ == 0.0 then
                        groundZ = baitCoords.z -- Fallback to bait height
                        DebugPrint('Ground detection failed for spawn, using bait Z: ' .. groundZ)
                    end

                    spawnCoords = vector3(spawnCoords.x, spawnCoords.y, groundZ + 1.0)

                    -- Load model and spawn animal
                    LoadModel(animalConfig.model)

                    -- Wait for model to load
                    local modelTimeout = 0
                    while not HasModelLoaded(animalConfig.model) and modelTimeout < 50 do
                        Wait(10)
                        modelTimeout = modelTimeout + 1
                    end

                    if HasModelLoaded(animalConfig.model) then
                        animal = CreatePed(28, animalConfig.model, spawnCoords.x, spawnCoords.y, spawnCoords.z, math.random(0, 360), true, true)

                        -- Wait for entity to exist
                        local entityTimeout = 0
                        while not DoesEntityExist(animal) and entityTimeout < 30 do
                            Wait(10)
                            entityTimeout = entityTimeout + 1
                        end

                        if DoesEntityExist(animal) then
                            DebugPrint('Successfully created animal entity: ' .. animal .. ' at attempt ' .. attempts)
                            break
                        else
                            DebugPrint('Failed to create animal entity at attempt ' .. attempts)
                            animal = nil
                        end
                    else
                        DebugPrint('Failed to load model: ' .. animalConfig.model)
                    end
                end

                if animal and DoesEntityExist(animal) then
                    -- Configure animal behavior
                    SetEntityAsMissionEntity(animal, true, true)
                    SetPedFleeAttributes(animal, 0, false)
                    SetPedCombatAttributes(animal, 17, true)
                    SetPedSeeingRange(animal, 40.0)
                    SetPedHearingRange(animal, 40.0)
                    SetPedAlertness(animal, 2)
                    SetEntityMaxHealth(animal, 200)
                    SetEntityHealth(animal, 200)
                    SetEntityInvincible(animal, false)

                    -- Make sure animal is on ground
                    PlaceObjectOnGroundProperly(animal)

                    -- Start with wandering behavior
                    TaskWanderInArea(animal, baitCoords.x, baitCoords.y, baitCoords.z, 20.0, 1.0, 1.0)

                    -- Store animal data
                    spawnedAnimals[animal] = {
                        model = animalConfig.model,
                        rewards = animalConfig.rewards,
                        skinned = false,
                        spawnTime = GetGameTimer(),
                        baitCoords = baitCoords,
                        baitId = baitId
                    }

                    totalAnimals = totalAnimals + 1
                    local animalCoords = GetEntityCoords(animal)
                    DebugPrint('Successfully spawned ' .. animalConfig.model .. ' at: ' .. tostring(animalCoords) .. ' (Entity: ' .. animal .. ')')

                    -- Start AI behavior thread for this animal
                    CreateThread(function()
                        local moveTimer = 0
                        while DoesEntityExist(animal) and not IsEntityDead(animal) and spawnedAnimals[animal] do
                            Wait(10000) -- Check every 10 seconds

                            local animalCoords = GetEntityCoords(animal)
                            local distanceToBait = #(animalCoords - baitCoords)

                            -- If animal is too far from bait, guide it back
                            if distanceToBait > 50.0 then
                                TaskGoToCoordAnyMeans(animal, baitCoords.x, baitCoords.y, baitCoords.z, 1.0, 0, false, 786603, 0xbf800000)
                                DebugPrint('Guiding ' .. animalConfig.model .. ' back to bait (distance: ' .. math.floor(distanceToBait) .. 'm)')
                            elseif distanceToBait > 30.0 then
                                -- Wander toward bait area
                                TaskWanderInArea(animal, baitCoords.x, baitCoords.y, baitCoords.z, 25.0, 1.0, 1.0)
                            end

                            moveTimer = moveTimer + 1
                            if moveTimer >= 3 then -- Every 30 seconds, reset behavior
                                TaskWanderInArea(animal, baitCoords.x, baitCoords.y, baitCoords.z, 20.0, 1.0, 1.0)
                                moveTimer = 0
                            end
                        end
                        DebugPrint('AI thread ended for animal: ' .. animal)
                    end)
                else
                    DebugPrint('Failed to spawn ' .. animalConfig.model .. ' after ' .. attempts .. ' attempts')
                end
            end
        end
    end

    if totalAnimals > 0 then
        ShowNotification('Wildlife attracted! ' .. totalAnimals .. ' animals are approaching your bait.', 'success')
        DebugPrint('Total animals spawned: ' .. totalAnimals)
    else
        ShowNotification('No animals were attracted to your bait this time. Try again later.', 'info')
        DebugPrint('No animals spawned - bad luck, max animals reached, or spawn failed')
    end
end

-- Bait Management Functions
local function PlaceBait()
    local playerPed = PlayerPedId()
    local coords = GetEntityCoords(playerPed)

    -- Check if player has tent
    if not playerTent then
        ShowNotification('You need to set up a tent first!', 'error')
        return
    end

    -- Check if within tent radius
    if #(coords - playerTent.coords) > Config.TentRadius then
        ShowNotification(Config.Notifications.tooFarFromTent, 'error')
        return
    end

    -- Check if player has enough bait (now requires 3)
    local baitCount = exports.ox_inventory:Search('count', Config.BaitItem)
    if baitCount < 3 then
        ShowNotification('You need at least 3 hunting bait to set up a proper hunting area!', 'error')
        return
    end

    -- Place bait
    local baitId = #playerBait + 1
    playerBait[baitId] = {
        coords = coords,
        placed = GetGameTimer()
    }

    -- Remove 3 bait from inventory
    TriggerServerEvent('qbox-hunting:server:removeItem', Config.BaitItem, 3)
    ShowNotification('Hunting area prepared with premium bait. Animals will arrive soon...', 'success')

    DebugPrint('Bait placed at: ' .. tostring(coords) .. ' (used 3 bait items)')

    -- Schedule animal spawning
    SetTimeout(Config.BaitSpawnDelay, function()
        DebugPrint('Starting animal spawn sequence for bait ID: ' .. baitId)
        SpawnAnimalsNearBait(baitId)
    end)

    -- Schedule bait cleanup
    SetTimeout(Config.BaitDuration, function()
        if playerBait[baitId] then
            playerBait[baitId] = nil
            ShowNotification(Config.Notifications.baitConsumed, 'info')
            DebugPrint('Bait ' .. baitId .. ' has been consumed and cleaned up')
        end
    end)
end

-- Hunting Functions
local function SkinAnimal(animal)
    local playerPed = PlayerPedId()

    -- Check if player has knife weapon equipped or in inventory
    local hasKnife = HasPedGotWeapon(playerPed, GetHashKey(Config.HuntingKnife), false) or HasItem(Config.HuntingKnife)
    if not hasKnife then
        ShowNotification('You need a knife to skin animals! Equip your hunting knife.', 'error')
        return
    end

    -- Check if animal is already skinned
    if not spawnedAnimals[animal] or spawnedAnimals[animal].skinned then
        ShowNotification(Config.Notifications.alreadySkinned, 'error')
        return
    end

    -- Check if animal is dead
    if not IsEntityDead(animal) then
        ShowNotification('The animal must be dead to skin it!', 'error')
        return
    end

    -- Equip knife if not already equipped
    if not HasPedGotWeapon(playerPed, GetHashKey(Config.HuntingKnife), false) then
        SetCurrentPedWeapon(playerPed, GetHashKey(Config.HuntingKnife), true)
        Wait(500) -- Give time for weapon to equip
    end

    -- Position player near animal and face it
    local animalCoords = GetEntityCoords(animal)
    local playerCoords = GetEntityCoords(playerPed)
    local heading = GetHeadingFromVector_2d(animalCoords.x - playerCoords.x, animalCoords.y - playerCoords.y)
    SetEntityHeading(playerPed, heading)

    -- Play kneeling/skinning animation
    LoadAnimDict(Config.KneelingAnim.dict)
    TaskPlayAnim(playerPed, Config.KneelingAnim.dict, Config.KneelingAnim.anim, 8.0, -8.0, -1, Config.KneelingAnim.flag, 0, false, false, false)

    -- Progress bar for skinning
    if lib.progressBar({
        duration = Config.SkinningTime,
        label = 'Carefully skinning animal...',
        useWhileDead = false,
        canCancel = true,
        disable = {
            car = true,
            move = true,
            combat = true,
        },
    }) then
        ClearPedTasks(playerPed)

        -- Mark as skinned
        spawnedAnimals[animal].skinned = true

        -- Determine pelt quality (new system)
        local peltQuality = DeterminePeltQuality()

        -- Give rewards based on animal type and pelt quality
        local rewards = spawnedAnimals[animal].rewards
        for _, reward in pairs(rewards) do
            local amount = math.random(reward.min, reward.max)

            -- Handle pelt rewards with quality system
            if reward.item == 'animal_pelt' then
                local qualityPelt = GetQualityPeltItem(peltQuality)
                if amount > 0 then
                    TriggerServerEvent('qbox-hunting:server:giveItem', qualityPelt, amount)
                end
            else
                -- Check for rare drops with chance system
                if reward.chance then
                    if math.random(100) <= reward.chance and amount > 0 then
                        TriggerServerEvent('qbox-hunting:server:giveItem', reward.item, amount)
                    end
                else
                    -- Regular drops (no chance system)
                    if amount > 0 then
                        TriggerServerEvent('qbox-hunting:server:giveItem', reward.item, amount)
                    end
                end
            end
        end

        ShowNotification('Animal skinned successfully! Pelt quality: ' .. peltQuality .. ' star' .. (peltQuality > 1 and 's' or ''), 'success')
        DebugPrint('Animal skinned, rewards given (pelt quality: ' .. peltQuality .. ')')

        -- Schedule corpse cleanup
        SetTimeout(Config.CorpseCleanupTime, function()
            if DoesEntityExist(animal) then
                DeleteEntity(animal)
                spawnedAnimals[animal] = nil
                DebugPrint('Animal corpse cleaned up')
            end
        end)
    else
        ClearPedTasks(playerPed)
        ShowNotification('Skinning cancelled', 'error')
    end
end

-- Cleanup Functions
function CleanupHuntingSession()
    -- Remove all spawned animals
    for animal, _ in pairs(spawnedAnimals) do
        if DoesEntityExist(animal) then
            DeleteEntity(animal)
        end
    end
    spawnedAnimals = {}

    -- Clear bait
    playerBait = {}

    DebugPrint('Hunting session cleaned up')
end

-- Pelt Quality System
local function DeterminePeltQuality()
    local roll = math.random(100)

    if roll <= 10 then
        return 3 -- Perfect quality (10% chance)
    elseif roll <= 35 then
        return 2 -- Good quality (25% chance)
    else
        return 1 -- Poor quality (65% chance)
    end
end

local function GetQualityPeltItem(quality)
    if quality == 3 then
        return 'animal_pelt_perfect'
    elseif quality == 2 then
        return 'animal_pelt_good'
    else
        return 'animal_pelt_poor'
    end
end

-- Event Handlers
RegisterNetEvent('qbox-hunting:client:useTentBag', function()
    PlaceTent()
end)

RegisterNetEvent('qbox-hunting:client:useBait', function()
    PlaceBait()
end)

RegisterNetEvent('qbox-hunting:client:pickupTent', function()
    PickupTent()
end)

-- Auto-detect skinning when near dead animals with knife equipped
CreateThread(function()
    while true do
        Wait(1000) -- Check every second

        if isLoggedIn then
            local playerPed = PlayerPedId()
            local coords = GetEntityCoords(playerPed)

            -- Check if player has knife equipped
            if HasPedGotWeapon(playerPed, GetHashKey(Config.HuntingKnife), false) then
                -- Find nearest dead animal
                local closestAnimal = nil
                local closestDistance = 3.0 -- Reduced distance for more precise interaction

                for animal, data in pairs(spawnedAnimals) do
                    if DoesEntityExist(animal) and IsEntityDead(animal) and not data.skinned then
                        local animalCoords = GetEntityCoords(animal)
                        local distance = #(coords - animalCoords)

                        if distance < closestDistance then
                            closestAnimal = animal
                            closestDistance = distance
                        end
                    end
                end

                -- Show prompt when near dead animal
                if closestAnimal then
                    lib.showTextUI('[E] Skin Animal', {
                        position = "top-center",
                        icon = 'knife',
                        style = {
                            borderRadius = 0,
                            backgroundColor = '#48BB78',
                            color = 'white'
                        }
                    })

                    if IsControlJustPressed(0, 38) then -- E key
                        lib.hideTextUI()
                        SkinAnimal(closestAnimal)
                    end
                else
                    lib.hideTextUI()
                end
            else
                lib.hideTextUI()
            end
        end
    end
end)

-- Keybind for removing tent (destroys tent, no bag returned)
RegisterCommand('removetent', function()
    if playerTent then
        RemoveTent(false)
    else
        ShowNotification('You don\'t have a tent placed!', 'error')
    end
end, false)

-- Keybind for picking up tent (returns bag to inventory)
RegisterCommand('pickuptent', function()
    PickupTent()
end, false)

-- Debug commands
RegisterCommand('forcespawn', function()
    if Config.Debug then
        if #playerBait > 0 then
            DebugPrint('Force spawning animals for debug')
            SpawnAnimalsNearBait(1)
        else
            ShowNotification('No bait placed! Place bait first.', 'error')
        end
    end
end, false)

RegisterCommand('clearhunt', function()
    if Config.Debug then
        DebugPrint('Force cleaning hunting session')
        CleanupHuntingSession()
        ShowNotification('Hunting session cleaned up', 'success')
    end
end, false)

RegisterCommand('huntdebug', function()
    if Config.Debug then
        local playerPed = PlayerPedId()
        local coords = GetEntityCoords(playerPed)

        DebugPrint('=== HUNTING DEBUG INFO ===')
        DebugPrint('Player coords: ' .. tostring(coords))
        DebugPrint('Has tent: ' .. tostring(playerTent ~= nil))
        if playerTent then
            DebugPrint('Tent coords: ' .. tostring(playerTent.coords))
        end
        DebugPrint('Bait count: ' .. #playerBait)
        DebugPrint('Spawned animals: ' .. #spawnedAnimals)

        local animalCount = 0
        for animal, data in pairs(spawnedAnimals) do
            if DoesEntityExist(animal) then
                animalCount = animalCount + 1
                local animalCoords = GetEntityCoords(animal)
                DebugPrint('Animal ' .. animal .. ' (' .. data.model .. ') at: ' .. tostring(animalCoords) .. ' - Dead: ' .. tostring(IsEntityDead(animal)))
            end
        end
        DebugPrint('Active animals: ' .. animalCount)
        DebugPrint('=== END DEBUG INFO ===')

        ShowNotification('Debug info printed to console', 'info')
    end
end, false)

-- Simple test spawn command
RegisterCommand('testspawn', function()
    if Config.Debug then
        local playerPed = PlayerPedId()
        local coords = GetEntityCoords(playerPed)
        local spawnCoords = GetOffsetFromEntityInWorldCoords(playerPed, 0.0, 10.0, 0.0)

        -- Get ground Z
        local groundZ = GetGroundZFor_3dCoord(spawnCoords.x, spawnCoords.y, spawnCoords.z + 50.0, false)
        if groundZ == 0.0 then
            groundZ = coords.z
        end

        spawnCoords = vector3(spawnCoords.x, spawnCoords.y, groundZ + 1.0)

        -- Load and spawn deer
        LoadModel('a_c_deer')
        local animal = CreatePed(28, 'a_c_deer', spawnCoords.x, spawnCoords.y, spawnCoords.z, 0.0, true, true)

        if DoesEntityExist(animal) then
            SetEntityAsMissionEntity(animal, true, true)
            PlaceObjectOnGroundProperly(animal)

            spawnedAnimals[animal] = {
                model = 'a_c_deer',
                rewards = Config.Animals[1].rewards,
                skinned = false,
                spawnTime = GetGameTimer()
            }

            DebugPrint('Test deer spawned at: ' .. tostring(spawnCoords) .. ' (Entity: ' .. animal .. ')')
            ShowNotification('Test deer spawned!', 'success')
        else
            DebugPrint('Failed to spawn test deer')
            ShowNotification('Failed to spawn test deer', 'error')
        end
    end
end, false)

-- Main thread for animal management
CreateThread(function()
    while true do
        Wait(30000) -- Check every 30 seconds

        -- Clean up old animals that haven't been skinned
        for animal, data in pairs(spawnedAnimals) do
            if DoesEntityExist(animal) and IsEntityDead(animal) and not data.skinned then
                if GetGameTimer() - data.spawnTime > Config.CorpseCleanupTime then
                    DeleteEntity(animal)
                    spawnedAnimals[animal] = nil
                    DebugPrint('Old animal corpse cleaned up')
                end
            end
        end
    end
end)

-- Additional event handlers
RegisterNetEvent('qbox-hunting:client:cleanupSession', function()
    CleanupHuntingSession()
    if playerTent then
        RemoveTent()
    end
end)

-- Cleanup on resource stop
AddEventHandler('onResourceStop', function(resourceName)
    if GetCurrentResourceName() == resourceName then
        CleanupHuntingSession()
        if playerTent then
            RemoveTent()
        end
    end
end)

-- Initialize on resource start
AddEventHandler('onResourceStart', function(resourceName)
    if GetCurrentResourceName() == resourceName then
        -- Reset all hunting data
        playerTent = nil
        playerBait = {}
        spawnedAnimals = {}
        lastTentPlacement = 0
        huntingZone = false

        DebugPrint('Qbox-Hunting client initialized')
    end
end)

-- Hunting Functions
local function SkinAnimal(animal)
    local playerPed = PlayerPedId()

    -- Check if player has hunting knife
    if not HasItem(Config.HuntingKnife) then
        ShowNotification(Config.Notifications.noKnife, 'error')
        return
    end

    -- Check if animal is already skinned
    if not spawnedAnimals[animal] or spawnedAnimals[animal].skinned then
        ShowNotification(Config.Notifications.alreadySkinned, 'error')
        return
    end

    -- Check if animal is dead
    if not IsEntityDead(animal) then
        ShowNotification('The animal must be dead to skin it!', 'error')
        return
    end

    -- Play skinning animation
    LoadAnimDict(Config.SkinningAnim.dict)
    TaskPlayAnim(playerPed, Config.SkinningAnim.dict, Config.SkinningAnim.anim, 8.0, -8.0, -1, Config.SkinningAnim.flag, 0, false, false, false)

    -- Progress bar for skinning
    QBCore.Functions.Progressbar('skin_animal', 'Skinning animal...', Config.SkinningTime, false, true, {
        disableMovement = true,
        disableCarMovement = true,
        disableMouse = false,
        disableCombat = true,
    }, {}, {}, {}, function() -- Done
        ClearPedTasks(playerPed)

        -- Mark as skinned
        spawnedAnimals[animal].skinned = true

        -- Give rewards
        local rewards = spawnedAnimals[animal].rewards
        for _, reward in pairs(rewards) do
            local amount = math.random(reward.min, reward.max)
            TriggerServerEvent('qb-hunting:server:giveItem', reward.item, amount)
        end

        ShowNotification(Config.Notifications.animalSkinned, 'success')
        DebugPrint('Animal skinned, rewards given')

        -- Schedule corpse cleanup
        SetTimeout(Config.CorpseCleanupTime, function()
            if DoesEntityExist(animal) then
                DeleteEntity(animal)
                spawnedAnimals[animal] = nil
                DebugPrint('Animal corpse cleaned up')
            end
        end)

    end, function() -- Cancel
        ClearPedTasks(playerPed)
        ShowNotification('Skinning cancelled', 'error')
    end)
end

-- Cleanup Functions
function CleanupHuntingSession()
    -- Remove all spawned animals
    for animal, _ in pairs(spawnedAnimals) do
        if DoesEntityExist(animal) then
            DeleteEntity(animal)
        end
    end
    spawnedAnimals = {}

    -- Clear bait
    playerBait = {}

    DebugPrint('Hunting session cleaned up')
end

-- Target System Integration
local function SetupTargets()
    if not Config.UseTarget then
        return
    end

    -- This would be implemented with ox_target or qb-target
    -- Example for ox_target:
    --[[
    exports.ox_target:addGlobalPed({
        {
            name = 'skin_animal',
            icon = 'fas fa-knife',
            label = 'Skin Animal',
            canInteract = function(entity)
                return spawnedAnimals[entity] and IsEntityDead(entity) and not spawnedAnimals[entity].skinned
            end,
            onSelect = function(data)
                SkinAnimal(data.entity)
            end
        }
    })
    --]]
end

-- Event Handlers
RegisterNetEvent('qb-hunting:client:useTentBag', function()
    PlaceTent()
end)

RegisterNetEvent('qb-hunting:client:useBait', function()
    PlaceBait()
end)

RegisterNetEvent('qb-hunting:client:useKnife', function()
    local playerPed = PlayerPedId()
    local coords = GetEntityCoords(playerPed)

    -- Find nearest dead animal
    local closestAnimal = nil
    local closestDistance = 5.0

    for animal, data in pairs(spawnedAnimals) do
        if DoesEntityExist(animal) and IsEntityDead(animal) and not data.skinned then
            local animalCoords = GetEntityCoords(animal)
            local distance = #(coords - animalCoords)

            if distance < closestDistance then
                closestAnimal = animal
                closestDistance = distance
            end
        end
    end

    if closestAnimal then
        SkinAnimal(closestAnimal)
    else
        ShowNotification('No dead animals nearby to skin!', 'error')
    end
end)
