local playerHuntingSessions = {}

-- Utility Functions
local function DebugPrint(message)
    if Config.Debug then
        print('[QB-Hunting] ' .. message)
    end
end

local function GetPlayer(source)
    return exports.qbx_core:GetPlayer(source)
end

local function HasItem(source, item, amount)
    amount = amount or 1
    local count = exports.ox_inventory:Search(source, 'count', item)
    return count >= amount
end

local function AddItem(source, item, amount, metadata)
    amount = amount or 1
    return exports.ox_inventory:AddItem(source, item, amount, metadata)
end

local function RemoveItem(source, item, amount)
    amount = amount or 1
    return exports.ox_inventory:RemoveItem(source, item, amount)
end

local function ShowNotification(source, message, type)
    TriggerClientEvent('ox_lib:notify', source, {
        title = 'Hunting',
        description = message,
        type = type or 'inform'
    })
end

-- Validation Functions
local function ValidateHuntingAction(source, requireLicense)
    local Player = GetPlayer(source)
    if not Player then
        return false, 'Player not found'
    end
    
    if requireLicense and Config.RequireLicense then
        if not HasItem(source, Config.LicenseItem) then
            return false, Config.Notifications.noLicense
        end
    end
    
    return true, nil
end

local function ValidateItemTransaction(source, item, amount, action)
    local Player = GetPlayer(source)
    if not Player then
        return false
    end
    
    if action == 'remove' then
        if not HasItem(source, item, amount) then
            DebugPrint('Player ' .. source .. ' does not have enough ' .. item)
            return false
        end
    end
    
    return true
end

-- Event Handlers
RegisterNetEvent('qb-hunting:server:removeItem', function(item, amount)
    local src = source
    amount = amount or 1
    
    if not ValidateItemTransaction(src, item, amount, 'remove') then
        return
    end
    
    if RemoveItem(src, item, amount) then
        DebugPrint('Removed ' .. amount .. 'x ' .. item .. ' from player ' .. src)
    else
        DebugPrint('Failed to remove ' .. item .. ' from player ' .. src)
    end
end)

RegisterNetEvent('qb-hunting:server:giveItem', function(item, amount, metadata)
    local src = source
    amount = amount or 1
    
    if AddItem(src, item, amount, metadata) then
        DebugPrint('Gave ' .. amount .. 'x ' .. item .. ' to player ' .. src)
        ShowNotification(src, 'You received ' .. amount .. 'x ' .. item, 'success')
    else
        DebugPrint('Failed to give ' .. item .. ' to player ' .. src)
        ShowNotification(src, 'Your inventory is full!', 'error')
    end
end)

RegisterNetEvent('qb-hunting:server:startHuntingSession', function()
    local src = source
    local valid, error = ValidateHuntingAction(src, true)
    
    if not valid then
        ShowNotification(src, error, 'error')
        return
    end
    
    -- Check if player already has an active session
    if playerHuntingSessions[src] then
        ShowNotification(src, 'You already have an active hunting session!', 'error')
        return
    end
    
    -- Create hunting session
    playerHuntingSessions[src] = {
        startTime = os.time(),
        tentPlaced = false,
        baitUsed = 0,
        animalsKilled = 0
    }
    
    DebugPrint('Started hunting session for player ' .. src)
end)

RegisterNetEvent('qb-hunting:server:endHuntingSession', function()
    local src = source
    
    if playerHuntingSessions[src] then
        playerHuntingSessions[src] = nil
        DebugPrint('Ended hunting session for player ' .. src)
    end
end)

RegisterNetEvent('qb-hunting:server:updateHuntingStats', function(statType, value)
    local src = source
    
    if not playerHuntingSessions[src] then
        return
    end
    
    if statType == 'tentPlaced' then
        playerHuntingSessions[src].tentPlaced = true
    elseif statType == 'baitUsed' then
        playerHuntingSessions[src].baitUsed = playerHuntingSessions[src].baitUsed + (value or 1)
    elseif statType == 'animalsKilled' then
        playerHuntingSessions[src].animalsKilled = playerHuntingSessions[src].animalsKilled + (value or 1)
    end
    
    DebugPrint('Updated hunting stats for player ' .. src .. ': ' .. statType .. ' = ' .. tostring(value))
end)

-- Player disconnect cleanup
RegisterNetEvent('QBCore:Server:OnPlayerUnload', function(src)
    if playerHuntingSessions[src] then
        playerHuntingSessions[src] = nil
        DebugPrint('Cleaned up hunting session for disconnected player ' .. src)
    end
end)

-- Admin Commands
RegisterCommand('huntingstats', function(source, args, rawCommand)
    local src = source
    local Player = GetPlayer(src)

    if not Player then
        return
    end

    -- Check if player has admin permissions (adjust based on your permission system)
    if not exports.qbx_core:HasPermission(src, 'admin') then
        ShowNotification(src, 'You don\'t have permission to use this command!', 'error')
        return
    end

    local targetId = tonumber(args[1]) or src

    if playerHuntingSessions[targetId] then
        local session = playerHuntingSessions[targetId]
        local sessionTime = os.time() - session.startTime

        TriggerClientEvent('chat:addMessage', src, {
            color = {255, 255, 0},
            multiline = true,
            args = {'Hunting Stats', 'Player: ' .. targetId .. '\nSession Time: ' .. sessionTime .. 's\nTent Placed: ' .. tostring(session.tentPlaced) .. '\nBait Used: ' .. session.baitUsed .. '\nAnimals Killed: ' .. session.animalsKilled}
        })
    else
        ShowNotification(src, 'Player has no active hunting session!', 'error')
    end
end, false)

-- Quick Test Command - Gives all hunting items
RegisterCommand('huntingkit', function(source, args, rawCommand)
    local src = source
    local Player = GetPlayer(src)

    if not Player then
        return
    end

    -- Check if player has admin permissions
    if not exports.qbx_core:HasPermission(src, 'admin') then
        ShowNotification(src, 'You don\'t have permission to use this command!', 'error')
        return
    end

    local targetId = tonumber(args[1]) or src
    local targetPlayer = GetPlayer(targetId)

    if not targetPlayer then
        ShowNotification(src, 'Player not found!', 'error')
        return
    end

    -- Give all hunting items
    local huntingItems = {
        {item = 'hunting_license', amount = 1},
        {item = 'hunting_bag', amount = 1},
        {item = 'hunting_bait', amount = 10},
        {item = 'hunting_knife', amount = 1}
    }

    local success = true
    for _, itemData in pairs(huntingItems) do
        if not AddItem(targetId, itemData.item, itemData.amount) then
            success = false
            DebugPrint('Failed to give ' .. itemData.item .. ' to player ' .. targetId)
        end
    end

    if success then
        ShowNotification(src, 'Hunting kit given to player ' .. targetId, 'success')
        ShowNotification(targetId, 'You received a complete hunting kit!', 'success')
        DebugPrint('Hunting kit given to player ' .. targetId)
    else
        ShowNotification(src, 'Some items failed to be given - check inventory space!', 'error')
    end
end, false)

-- Callback for checking hunting license
lib.callback.register('qb-hunting:server:hasLicense', function(source)
    return HasItem(source, Config.LicenseItem)
end)

-- Callback for getting player hunting stats
lib.callback.register('qb-hunting:server:getHuntingStats', function(source)
    return playerHuntingSessions[source] or nil
end)

-- Resource start/stop handlers
AddEventHandler('onResourceStart', function(resourceName)
    if GetCurrentResourceName() == resourceName then
        DebugPrint('QB-Hunting server started')
        
        -- Clear any existing hunting sessions
        playerHuntingSessions = {}
    end
end)

AddEventHandler('onResourceStop', function(resourceName)
    if GetCurrentResourceName() == resourceName then
        DebugPrint('QB-Hunting server stopped')
        
        -- Notify all players with active sessions
        for playerId, _ in pairs(playerHuntingSessions) do
            TriggerClientEvent('qb-hunting:client:cleanupSession', playerId)
        end
        
        playerHuntingSessions = {}
    end
end)

-- Item Usage Exports for ox_inventory
exports('hunting_bag', function(event, item, inventory, slot, data)
    if event == 'usingItem' then
        local src = inventory.id
        local valid, error = ValidateHuntingAction(src, true)

        if not valid then
            ShowNotification(src, error, 'error')
            return false
        end

        TriggerClientEvent('qb-hunting:client:useTentBag', src)
        return true
    end
end)

exports('hunting_bait', function(event, item, inventory, slot, data)
    if event == 'usingItem' then
        local src = inventory.id
        local valid, error = ValidateHuntingAction(src, true)

        if not valid then
            ShowNotification(src, error, 'error')
            return false
        end

        TriggerClientEvent('qb-hunting:client:useBait', src)
        return true
    end
end)

exports('hunting_knife', function(event, item, inventory, slot, data)
    if event == 'usingItem' then
        local src = inventory.id
        local valid, error = ValidateHuntingAction(src, false) -- No license required for knife

        if not valid then
            ShowNotification(src, error, 'error')
            return false
        end

        TriggerClientEvent('qb-hunting:client:useKnife', src)
        return true
    end
end)
