--[[
    Example Shop Configuration for QB-Hunting
    
    This file shows how to integrate hunting items into various shop systems.
    Copy the relevant sections to your shop configuration files.
]]

-- Example for qb-shops integration
Config.Shops = {
    ['hunting_shop'] = {
        name = 'Hunting Supply Store',
        coords = vector4(-679.9, 5834.0, 17.3, 225.0), -- Paleto Bay
        ped = 'a_m_m_hillbilly_01',
        items = {
            [1] = {
                name = 'hunting_license',
                price = 500,
                amount = 1,
                info = {},
                type = 'item',
                slot = 1,
            },
            [2] = {
                name = 'hunting_bag',
                price = 150,
                amount = 1,
                info = {},
                type = 'item',
                slot = 2,
            },
            [3] = {
                name = 'hunting_bait',
                price = 25,
                amount = 10,
                info = {},
                type = 'item',
                slot = 3,
            },
            [4] = {
                name = 'hunting_knife',
                price = 75,
                amount = 1,
                info = {},
                type = 'item',
                slot = 4,
            },
        },
    },
    
    ['hunting_buyer'] = {
        name = 'Hunting Goods Buyer',
        coords = vector4(-679.9, 5830.0, 17.3, 45.0), -- Paleto Bay
        ped = 'a_m_m_farmer_01',
        items = {
            [1] = {
                name = 'raw_meat',
                price = 15,
                amount = 50,
                info = {},
                type = 'item',
                slot = 1,
            },
            [2] = {
                name = 'animal_pelt',
                price = 45,
                amount = 50,
                info = {},
                type = 'item',
                slot = 2,
            },
            [3] = {
                name = 'deer_antlers',
                price = 200,
                amount = 10,
                info = {},
                type = 'item',
                slot = 3,
            },
            [4] = {
                name = 'boar_tusk',
                price = 150,
                amount = 10,
                info = {},
                type = 'item',
                slot = 4,
            },
            [5] = {
                name = 'mountain_lion_tooth',
                price = 300,
                amount = 10,
                info = {},
                type = 'item',
                slot = 5,
            },
        },
    },
}

-- Example for ox_shops integration
return {
    General = {
        name = 'Hunting Supply Store',
        blip = {
            id = 442, sprite = 442, colour = 69, scale = 0.8
        },
        inventory = {
            { name = 'hunting_license', price = 500 },
            { name = 'hunting_bag', price = 150 },
            { name = 'hunting_bait', price = 25 },
            { name = 'hunting_knife', price = 75 },
        },
        locations = {
            vec3(-679.9, 5834.0, 17.3), -- Paleto Bay
            vec3(1961.0, 5179.0, 47.9), -- Grapeseed
        },
        targets = {
            { ped = `a_m_m_hillbilly_01` }
        }
    },
    
    HuntingBuyer = {
        name = 'Hunting Goods Buyer',
        blip = {
            id = 141, sprite = 141, colour = 69, scale = 0.8
        },
        inventory = {
            { name = 'raw_meat', price = 15 },
            { name = 'animal_pelt', price = 45 },
            { name = 'deer_antlers', price = 200 },
            { name = 'boar_tusk', price = 150 },
            { name = 'mountain_lion_tooth', price = 300 },
        },
        locations = {
            vec3(-679.9, 5830.0, 17.3), -- Paleto Bay
        },
        targets = {
            { ped = `a_m_m_farmer_01` }
        }
    }
}

--[[
    Example Job-Restricted License Sales
    
    Add this to your government/police job script to allow license sales:
]]

-- Example for qb-policejob or similar
RegisterNetEvent('qb-hunting:server:sellLicense', function(targetId)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    local Target = QBCore.Functions.GetPlayer(targetId)
    
    if not Player or not Target then return end
    
    -- Check if player is authorized to sell licenses
    if Player.PlayerData.job.name ~= 'police' and Player.PlayerData.job.name ~= 'ranger' then
        TriggerClientEvent('QBCore:Notify', src, 'You are not authorized to sell hunting licenses!', 'error')
        return
    end
    
    -- Check if target already has license
    if Target.Functions.GetItemByName('hunting_license') then
        TriggerClientEvent('QBCore:Notify', src, 'This person already has a hunting license!', 'error')
        return
    end
    
    -- Give license to target
    if Target.Functions.AddItem('hunting_license', 1) then
        TriggerClientEvent('QBCore:Notify', src, 'Hunting license issued successfully!', 'success')
        TriggerClientEvent('QBCore:Notify', targetId, 'You have received a hunting license!', 'success')
        
        -- Optional: Add to government funds
        -- exports['qb-management']:AddMoney('government', 500)
    else
        TriggerClientEvent('QBCore:Notify', src, 'Failed to issue license - target inventory full!', 'error')
    end
end)

--[[
    Example Hunting Areas Configuration
    
    Add these coordinates to your Config.RestrictedAreas to prevent hunting in populated areas:
]]

Config.RestrictedAreas = {
    -- Los Santos City Center
    {coords = vector3(240.0, -1379.0, 33.0), radius = 500.0},
    
    -- Los Santos Airport
    {coords = vector3(-1037.0, -2737.0, 20.0), radius = 800.0},
    
    -- Sandy Shores
    {coords = vector3(1836.0, 3672.0, 34.0), radius = 300.0},
    
    -- Paleto Bay Town
    {coords = vector3(-276.0, 6635.0, 7.0), radius = 200.0},
    
    -- Military Base
    {coords = vector3(-2047.0, 2951.0, 32.0), radius = 1000.0},
    
    -- Prison
    {coords = vector3(1845.0, 2585.0, 46.0), radius = 500.0},
}

--[[
    Recommended Hunting Zones (Good areas for hunting):
    
    - Mount Chiliad Area: vector3(-1119.0, 4920.0, 218.0)
    - Raton Canyon: vector3(-1654.0, 4445.0, 2.0)
    - Grand Senora Desert: vector3(1747.0, 3239.0, 41.0)
    - Tongva Hills: vector3(-1958.0, 2611.0, 3.0)
    - Mount Gordo: vector3(2877.0, 5911.0, 369.0)
]]
