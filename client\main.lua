local PlayerData = {}
local isLoggedIn = false

-- Player state variables
local playerTent = nil
local playerBait = {}
local spawnedAnimals = {}
local lastTentPlacement = 0
local huntingZone = false

-- Initialize player data
RegisterNetEvent('QBCore:Client:OnPlayerLoaded', function()
    PlayerData = exports.qbx_core:GetPlayerData()
    isLoggedIn = true
end)

RegisterNetEvent('QBCore:Client:OnPlayerUnload', function()
    PlayerData = {}
    isLoggedIn = false
    CleanupHuntingSession()
end)

RegisterNetEvent('QBCore:Player:SetPlayerData', function(val)
    PlayerData = val
end)

-- Utility Functions
local function DebugPrint(message)
    if Config.Debug then
        print('[QB-Hunting] ' .. message)
    end
end

local function ShowNotification(message, type)
    exports.qbx_core:Notify(message, type or 'primary', 5000)
end

local function HasItem(item)
    local hasItem = exports.ox_inventory:Search('count', item) > 0
    return hasItem
end

local function IsInRestrictedArea(coords)
    for _, area in pairs(Config.RestrictedAreas) do
        if #(coords - area.coords) < area.radius then
            return true
        end
    end
    return false
end

local function LoadAnimDict(dict)
    if not HasAnimDictLoaded(dict) then
        RequestAnimDict(dict)
        while not HasAnimDictLoaded(dict) do
            Wait(1)
        end
    end
end

local function LoadModel(model)
    if not HasModelLoaded(model) then
        RequestModel(model)
        while not HasModelLoaded(model) do
            Wait(1)
        end
    end
end

-- Tent Management Functions
local function PlaceTent()
    local playerPed = PlayerPedId()
    local coords = GetEntityCoords(playerPed)
    
    -- Check if player has license
    if Config.RequireLicense and not HasItem(Config.LicenseItem) then
        ShowNotification(Config.Notifications.noLicense, 'error')
        return
    end
    
    -- Check if in restricted area
    if IsInRestrictedArea(coords) then
        ShowNotification('You cannot hunt in this area!', 'error')
        return
    end
    
    -- Check cooldown
    if GetGameTimer() - lastTentPlacement < Config.TentPlacementCooldown then
        ShowNotification(Config.Notifications.tentCooldown, 'error')
        return
    end
    
    -- Check if player already has a tent
    if playerTent then
        ShowNotification(Config.Notifications.alreadyHasTent, 'error')
        return
    end
    
    -- Play animation
    LoadAnimDict(Config.TentPlaceAnim.dict)
    TaskPlayAnim(playerPed, Config.TentPlaceAnim.dict, Config.TentPlaceAnim.anim, 8.0, -8.0, -1, Config.TentPlaceAnim.flag, 0, false, false, false)
    
    -- Progress bar
    if lib.progressBar({
        duration = 5000,
        label = 'Setting up hunting tent...',
        useWhileDead = false,
        canCancel = true,
        disable = {
            car = true,
            move = true,
            combat = true,
        },
    }) then
        ClearPedTasks(playerPed)
        
        -- Load tent model
        LoadModel(Config.TentModel)
        
        -- Create tent
        local tentCoords = GetOffsetFromEntityInWorldCoords(playerPed, 0.0, 2.0, 0.0)
        local tent = CreateObject(Config.TentModel, tentCoords.x, tentCoords.y, tentCoords.z, true, true, true)
        PlaceObjectOnGroundProperly(tent)
        FreezeEntityPosition(tent, true)
        
        playerTent = {
            object = tent,
            coords = GetEntityCoords(tent)
        }
        
        lastTentPlacement = GetGameTimer()
        huntingZone = true
        
        -- Remove hunting bag from inventory
        TriggerServerEvent('qb-hunting:server:removeItem', 'hunting_bag', 1)
        ShowNotification(Config.Notifications.tentPlaced, 'success')
        
        DebugPrint('Tent placed at: ' .. tostring(tentCoords))
    else
        ClearPedTasks(playerPed)
        ShowNotification('Tent placement cancelled', 'error')
    end
end

local function RemoveTent()
    if not playerTent then
        return
    end

    if DoesEntityExist(playerTent.object) then
        DeleteEntity(playerTent.object)
    end

    -- Clean up any active bait and animals
    CleanupHuntingSession()

    playerTent = nil
    huntingZone = false

    ShowNotification(Config.Notifications.tentRemoved, 'success')
    DebugPrint('Tent removed')
end

-- Bait Management Functions
local function PlaceBait()
    local playerPed = PlayerPedId()
    local coords = GetEntityCoords(playerPed)

    -- Check if player has tent
    if not playerTent then
        ShowNotification('You need to set up a tent first!', 'error')
        return
    end

    -- Check if within tent radius
    if #(coords - playerTent.coords) > Config.TentRadius then
        ShowNotification(Config.Notifications.tooFarFromTent, 'error')
        return
    end

    -- Check if player has bait
    if not HasItem(Config.BaitItem) then
        ShowNotification('You need hunting bait!', 'error')
        return
    end

    -- Place bait
    local baitId = #playerBait + 1
    playerBait[baitId] = {
        coords = coords,
        placed = GetGameTimer()
    }

    -- Remove bait from inventory
    TriggerServerEvent('qb-hunting:server:removeItem', Config.BaitItem, 1)
    ShowNotification(Config.Notifications.baitPlaced, 'success')

    -- Schedule animal spawning
    SetTimeout(Config.BaitSpawnDelay, function()
        SpawnAnimalsNearBait(baitId)
    end)

    -- Schedule bait cleanup
    SetTimeout(Config.BaitDuration, function()
        if playerBait[baitId] then
            playerBait[baitId] = nil
            ShowNotification(Config.Notifications.baitConsumed, 'info')
        end
    end)

    DebugPrint('Bait placed at: ' .. tostring(coords))
end

-- Animal Management Functions
local function SpawnAnimalsNearBait(baitId)
    if not playerBait[baitId] then
        return
    end

    local baitCoords = playerBait[baitId].coords
    local totalAnimals = 0

    for _, animalConfig in pairs(Config.Animals) do
        if math.random(100) <= animalConfig.spawnChance and totalAnimals < Config.MaxAnimalsPerZone then
            local spawnCount = math.random(1, animalConfig.maxSpawns)

            for i = 1, spawnCount do
                if totalAnimals >= Config.MaxAnimalsPerZone then
                    break
                end

                -- Find spawn position around bait
                local angle = math.random() * 2 * math.pi
                local distance = math.random(10, Config.BaitRadius)
                local spawnCoords = vector3(
                    baitCoords.x + math.cos(angle) * distance,
                    baitCoords.y + math.sin(angle) * distance,
                    baitCoords.z
                )

                -- Get ground Z coordinate
                local groundZ = GetGroundZFor_3dCoord(spawnCoords.x, spawnCoords.y, spawnCoords.z + 50.0, false)
                spawnCoords = vector3(spawnCoords.x, spawnCoords.y, groundZ)

                -- Load and spawn animal
                LoadModel(animalConfig.model)
                local animal = CreatePed(28, animalConfig.model, spawnCoords.x, spawnCoords.y, spawnCoords.z, math.random(0, 360), true, true)

                -- Configure animal
                SetEntityAsMissionEntity(animal, true, true)
                SetPedFleeAttributes(animal, 0, false)
                SetPedCombatAttributes(animal, 17, true)
                SetPedSeeingRange(animal, 50.0)
                SetPedHearingRange(animal, 50.0)
                SetPedAlertness(animal, 3)

                -- Make animal move towards bait
                TaskGoToCoordAnyMeans(animal, baitCoords.x, baitCoords.y, baitCoords.z, 1.0, 0, false, 786603, 0xbf800000)

                -- Store animal data
                spawnedAnimals[animal] = {
                    model = animalConfig.model,
                    rewards = animalConfig.rewards,
                    skinned = false,
                    spawnTime = GetGameTimer()
                }

                totalAnimals = totalAnimals + 1
                DebugPrint('Spawned ' .. animalConfig.model .. ' at: ' .. tostring(spawnCoords))
            end
        end
    end

    if totalAnimals > 0 then
        ShowNotification(Config.Notifications.animalSpawned, 'success')
    end
end

-- Hunting Functions
local function SkinAnimal(animal)
    local playerPed = PlayerPedId()

    -- Check if player has hunting knife
    if not HasItem(Config.HuntingKnife) then
        ShowNotification(Config.Notifications.noKnife, 'error')
        return
    end

    -- Check if animal is already skinned
    if not spawnedAnimals[animal] or spawnedAnimals[animal].skinned then
        ShowNotification(Config.Notifications.alreadySkinned, 'error')
        return
    end

    -- Check if animal is dead
    if not IsEntityDead(animal) then
        ShowNotification('The animal must be dead to skin it!', 'error')
        return
    end

    -- Play skinning animation
    LoadAnimDict(Config.SkinningAnim.dict)
    TaskPlayAnim(playerPed, Config.SkinningAnim.dict, Config.SkinningAnim.anim, 8.0, -8.0, -1, Config.SkinningAnim.flag, 0, false, false, false)

    -- Progress bar for skinning
    if lib.progressBar({
        duration = Config.SkinningTime,
        label = 'Skinning animal...',
        useWhileDead = false,
        canCancel = true,
        disable = {
            car = true,
            move = true,
            combat = true,
        },
    }) then
        ClearPedTasks(playerPed)

        -- Mark as skinned
        spawnedAnimals[animal].skinned = true

        -- Give rewards
        local rewards = spawnedAnimals[animal].rewards
        for _, reward in pairs(rewards) do
            local amount = math.random(reward.min, reward.max)

            -- Check for rare drops with chance system
            if reward.chance then
                if math.random(100) <= reward.chance and amount > 0 then
                    TriggerServerEvent('qb-hunting:server:giveItem', reward.item, amount)
                end
            else
                -- Regular drops (no chance system)
                if amount > 0 then
                    TriggerServerEvent('qb-hunting:server:giveItem', reward.item, amount)
                end
            end
        end

        ShowNotification(Config.Notifications.animalSkinned, 'success')
        DebugPrint('Animal skinned, rewards given')

        -- Schedule corpse cleanup
        SetTimeout(Config.CorpseCleanupTime, function()
            if DoesEntityExist(animal) then
                DeleteEntity(animal)
                spawnedAnimals[animal] = nil
                DebugPrint('Animal corpse cleaned up')
            end
        end)
    else
        ClearPedTasks(playerPed)
        ShowNotification('Skinning cancelled', 'error')
    end
end

-- Cleanup Functions
function CleanupHuntingSession()
    -- Remove all spawned animals
    for animal, _ in pairs(spawnedAnimals) do
        if DoesEntityExist(animal) then
            DeleteEntity(animal)
        end
    end
    spawnedAnimals = {}

    -- Clear bait
    playerBait = {}

    DebugPrint('Hunting session cleaned up')
end

-- Event Handlers
RegisterNetEvent('qb-hunting:client:useTentBag', function()
    PlaceTent()
end)

RegisterNetEvent('qb-hunting:client:useBait', function()
    PlaceBait()
end)

RegisterNetEvent('qb-hunting:client:useKnife', function()
    local playerPed = PlayerPedId()
    local coords = GetEntityCoords(playerPed)

    -- Find nearest dead animal
    local closestAnimal = nil
    local closestDistance = 5.0

    for animal, data in pairs(spawnedAnimals) do
        if DoesEntityExist(animal) and IsEntityDead(animal) and not data.skinned then
            local animalCoords = GetEntityCoords(animal)
            local distance = #(coords - animalCoords)

            if distance < closestDistance then
                closestAnimal = animal
                closestDistance = distance
            end
        end
    end

    if closestAnimal then
        SkinAnimal(closestAnimal)
    else
        ShowNotification('No dead animals nearby to skin!', 'error')
    end
end)

-- Keybind for removing tent
RegisterCommand('removetent', function()
    if playerTent then
        RemoveTent()
    else
        ShowNotification('You don\'t have a tent placed!', 'error')
    end
end, false)

-- Main thread for animal management
CreateThread(function()
    while true do
        Wait(30000) -- Check every 30 seconds

        -- Clean up old animals that haven't been skinned
        for animal, data in pairs(spawnedAnimals) do
            if DoesEntityExist(animal) and IsEntityDead(animal) and not data.skinned then
                if GetGameTimer() - data.spawnTime > Config.CorpseCleanupTime then
                    DeleteEntity(animal)
                    spawnedAnimals[animal] = nil
                    DebugPrint('Old animal corpse cleaned up')
                end
            end
        end
    end
end)

-- Additional event handlers
RegisterNetEvent('qb-hunting:client:cleanupSession', function()
    CleanupHuntingSession()
    if playerTent then
        RemoveTent()
    end
end)

-- Cleanup on resource stop
AddEventHandler('onResourceStop', function(resourceName)
    if GetCurrentResourceName() == resourceName then
        CleanupHuntingSession()
        if playerTent then
            RemoveTent()
        end
    end
end)

-- Initialize on resource start
AddEventHandler('onResourceStart', function(resourceName)
    if GetCurrentResourceName() == resourceName then
        -- Reset all hunting data
        playerTent = nil
        playerBait = {}
        spawnedAnimals = {}
        lastTentPlacement = 0
        huntingZone = false

        DebugPrint('QB-Hunting client initialized')
    end
end)

-- Hunting Functions
local function SkinAnimal(animal)
    local playerPed = PlayerPedId()

    -- Check if player has hunting knife
    if not HasItem(Config.HuntingKnife) then
        ShowNotification(Config.Notifications.noKnife, 'error')
        return
    end

    -- Check if animal is already skinned
    if not spawnedAnimals[animal] or spawnedAnimals[animal].skinned then
        ShowNotification(Config.Notifications.alreadySkinned, 'error')
        return
    end

    -- Check if animal is dead
    if not IsEntityDead(animal) then
        ShowNotification('The animal must be dead to skin it!', 'error')
        return
    end

    -- Play skinning animation
    LoadAnimDict(Config.SkinningAnim.dict)
    TaskPlayAnim(playerPed, Config.SkinningAnim.dict, Config.SkinningAnim.anim, 8.0, -8.0, -1, Config.SkinningAnim.flag, 0, false, false, false)

    -- Progress bar for skinning
    QBCore.Functions.Progressbar('skin_animal', 'Skinning animal...', Config.SkinningTime, false, true, {
        disableMovement = true,
        disableCarMovement = true,
        disableMouse = false,
        disableCombat = true,
    }, {}, {}, {}, function() -- Done
        ClearPedTasks(playerPed)

        -- Mark as skinned
        spawnedAnimals[animal].skinned = true

        -- Give rewards
        local rewards = spawnedAnimals[animal].rewards
        for _, reward in pairs(rewards) do
            local amount = math.random(reward.min, reward.max)
            TriggerServerEvent('qb-hunting:server:giveItem', reward.item, amount)
        end

        ShowNotification(Config.Notifications.animalSkinned, 'success')
        DebugPrint('Animal skinned, rewards given')

        -- Schedule corpse cleanup
        SetTimeout(Config.CorpseCleanupTime, function()
            if DoesEntityExist(animal) then
                DeleteEntity(animal)
                spawnedAnimals[animal] = nil
                DebugPrint('Animal corpse cleaned up')
            end
        end)

    end, function() -- Cancel
        ClearPedTasks(playerPed)
        ShowNotification('Skinning cancelled', 'error')
    end)
end

-- Cleanup Functions
function CleanupHuntingSession()
    -- Remove all spawned animals
    for animal, _ in pairs(spawnedAnimals) do
        if DoesEntityExist(animal) then
            DeleteEntity(animal)
        end
    end
    spawnedAnimals = {}

    -- Clear bait
    playerBait = {}

    DebugPrint('Hunting session cleaned up')
end

-- Target System Integration
local function SetupTargets()
    if not Config.UseTarget then
        return
    end

    -- This would be implemented with ox_target or qb-target
    -- Example for ox_target:
    --[[
    exports.ox_target:addGlobalPed({
        {
            name = 'skin_animal',
            icon = 'fas fa-knife',
            label = 'Skin Animal',
            canInteract = function(entity)
                return spawnedAnimals[entity] and IsEntityDead(entity) and not spawnedAnimals[entity].skinned
            end,
            onSelect = function(data)
                SkinAnimal(data.entity)
            end
        }
    })
    --]]
end

-- Event Handlers
RegisterNetEvent('qb-hunting:client:useTentBag', function()
    PlaceTent()
end)

RegisterNetEvent('qb-hunting:client:useBait', function()
    PlaceBait()
end)

RegisterNetEvent('qb-hunting:client:useKnife', function()
    local playerPed = PlayerPedId()
    local coords = GetEntityCoords(playerPed)

    -- Find nearest dead animal
    local closestAnimal = nil
    local closestDistance = 5.0

    for animal, data in pairs(spawnedAnimals) do
        if DoesEntityExist(animal) and IsEntityDead(animal) and not data.skinned then
            local animalCoords = GetEntityCoords(animal)
            local distance = #(coords - animalCoords)

            if distance < closestDistance then
                closestAnimal = animal
                closestDistance = distance
            end
        end
    end

    if closestAnimal then
        SkinAnimal(closestAnimal)
    else
        ShowNotification('No dead animals nearby to skin!', 'error')
    end
end)
