-- QB-Hunting Database Setup
-- This script currently doesn't require database tables as all data is handled in-memory
-- However, this file is included for future expansion possibilities

-- Example table for persistent hunting statistics (optional)
-- Uncomment if you want to track hunting statistics across server restarts

/*
CREATE TABLE IF NOT EXISTS `hunting_stats` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `citizenid` varchar(50) NOT NULL,
    `total_animals_killed` int(11) DEFAULT 0,
    `total_meat_harvested` int(11) DEFAULT 0,
    `total_pelts_harvested` int(11) DEFAULT 0,
    `rare_items_found` int(11) DEFAULT 0,
    `hunting_sessions` int(11) DEFAULT 0,
    `last_hunt_date` timestamp NULL DEFAULT NULL,
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `citizenid` (`citizenid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
*/

-- Example table for hunting licenses (if you want persistent license tracking)
-- Uncomment if you want licenses to persist across server restarts

/*
CREATE TABLE IF NOT EXISTS `hunting_licenses` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `citizenid` varchar(50) NOT NULL,
    `license_type` varchar(50) DEFAULT 'basic',
    `issued_date` timestamp DEFAULT CURRENT_TIMESTAMP,
    `expiry_date` timestamp NULL DEFAULT NULL,
    `issued_by` varchar(50) DEFAULT NULL,
    `status` enum('active','expired','revoked') DEFAULT 'active',
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `citizenid` (`citizenid`),
    KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
*/

-- Note: The current implementation uses in-memory storage for simplicity and performance
-- All hunting sessions are automatically cleaned up when players disconnect or the resource restarts
-- This approach reduces database load and provides better performance for temporary hunting activities
