return {
    -- Hunting License
    ['hunting_license'] = {
        label = 'Hunting License',
        weight = 50,
        stack = false,
        close = true,
        description = 'Official hunting license required for legal hunting activities',
        client = {
            image = 'hunting_license.png',
        }
    },

    -- Hunting Equipment
    ['hunting_bag'] = {
        label = 'Hunting Bag',
        weight = 2000,
        stack = false,
        close = true,
        description = 'A sturdy bag containing everything needed to set up a hunting camp',
        client = {
            image = 'hunting_bag.png',
        },
        server = {
            export = 'qbox-hunting.hunting_bag'
        }
    },

    ['hunting_bait'] = {
        label = 'Hunting Bait',
        weight = 500,
        stack = true,
        close = true,
        description = 'Special bait designed to attract wild animals',
        client = {
            image = 'hunting_bait.png',
        },
        server = {
            export = 'qbox-hunting.hunting_bait'
        }
    },

    ['hunting_knife'] = {
        label = 'Hunting Knife',
        weight = 300,
        stack = false,
        close = true,
        description = 'A sharp knife specifically designed for skinning animals',
        client = {
            image = 'hunting_knife.png',
        },
        server = {
            export = 'qbox-hunting.hunting_knife'
        }
    },

    -- Animal Products
    ['raw_meat'] = {
        label = 'Raw Meat',
        weight = 200,
        stack = true,
        close = true,
        description = 'Fresh meat from a hunted animal. Can be cooked or sold.',
        client = {
            image = 'raw_meat.png',
        }
    },

    ['animal_pelt'] = {
        label = 'Animal Pelt',
        weight = 800,
        stack = true,
        close = true,
        description = 'High-quality animal pelt that can be sold or crafted into leather goods',
        client = {
            image = 'animal_pelt.png',
        }
    },

    -- Processed Products (optional for future expansion)
    ['cooked_meat'] = {
        label = 'Cooked Meat',
        weight = 150,
        stack = true,
        close = true,
        description = 'Properly cooked meat that restores health when consumed',
        client = {
            image = 'cooked_meat.png',
        }
    },

    ['leather'] = {
        label = 'Leather',
        weight = 600,
        stack = true,
        close = true,
        description = 'Processed leather from animal pelts',
        client = {
            image = 'leather.png',
        }
    },

    -- Special Items (rare drops)
    ['deer_antlers'] = {
        label = 'Deer Antlers',
        weight = 1200,
        stack = true,
        close = true,
        description = 'Rare deer antlers that can be sold for a high price',
        client = {
            image = 'deer_antlers.png',
        }
    },

    ['mountain_lion_tooth'] = {
        label = 'Mountain Lion Tooth',
        weight = 50,
        stack = true,
        close = true,
        description = 'A rare trophy from a mountain lion',
        client = {
            image = 'mountain_lion_tooth.png',
        }
    },

    ['boar_tusk'] = {
        label = 'Boar Tusk',
        weight = 150,
        stack = true,
        close = true,
        description = 'Sharp tusk from a wild boar',
        client = {
            image = 'boar_tusk.png',
        }
    },

    -- Hunting Weapons
    ['weapon_sniperrifle'] = {
        label = 'Hunting Rifle',
        weight = 3000,
        stack = false,
        close = true,
        description = 'A high-powered rifle perfect for hunting large game',
        client = {
            image = 'weapon_sniperrifle.png',
        }
    },

    ['sniper_ammo'] = {
        label = 'Rifle Ammunition',
        weight = 50,
        stack = true,
        close = true,
        description = 'High-caliber ammunition for hunting rifles',
        client = {
            image = 'sniper_ammo.png',
        }
    }
}
